'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Microscope, BookOpen, Users, GraduationCap, Code, ExternalLink } from 'lucide-react';

import { v2NavItems } from '@/constants/v2/navigation';

const iconMap = {
  home: Home,
  microscope: Microscope,
  'book-open': BookO<PERSON>,
  users: Users,
  'graduation-cap': GraduationCap,
  code: Code,
  'external-link': ExternalLink,
};

const V2Header = () => {
  const pathname = usePathname();

  return (
    <header className="v2-nav sticky top-0 z-50">
      <div className="v2-container">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/v2/home" className="text-2xl font-bold text-white">
            Yangtze Research Group
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-2">
            {v2NavItems.map((item) => {
              const Icon = iconMap[item.icon as keyof typeof iconMap];
              const isActive = pathname === item.path;

              return (
                <Link
                  key={item.name}
                  href={item.path}
                  className={`v2-nav-item ${isActive ? 'active' : ''}`}
                >
                  <Icon size={18} />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Version Switch */}
          <div className="flex items-center space-x-4">
            <Link
              href="/v1/home"
              className="text-sm text-white/80 hover:text-white transition-colors px-3 py-1 rounded border border-white/20 hover:border-white/40"
            >
              切换到经典版本
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default V2Header;
