import V2BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2ResearchPage() {
  return (
    <V2BaseLayout>
      <div>
        <h1 className="v2-heading text-4xl mb-8 text-white text-center">
          研究领域
        </h1>

        <div className="v2-card p-8 max-w-4xl mx-auto">
          <div className="space-y-6">
            <div>
              <h2 className="v2-heading text-2xl mb-4">理论化学</h2>
              <p className="v2-text">
                我们专注于开发理论方法来理解分子系统和化学过程，包括量子力学方法、电子结构理论和分子动力学模拟等。
              </p>
            </div>

            <div>
              <h2 className="v2-heading text-2xl mb-4">计算方法</h2>
              <p className="v2-text">
                开发用于大规模分子系统的先进计算方法，包括线性标度算法、高性能计算和机器学习应用。
              </p>
            </div>

            <div>
              <h2 className="v2-heading text-2xl mb-4">材料科学</h2>
              <p className="v2-text">
                在分子水平上理解材料的性质，研究纳米材料、电子性质和表面化学。
              </p>
            </div>

            <div>
              <h2 className="v2-heading text-2xl mb-4">生物系统</h2>
              <p className="v2-text">
                对生物分子和过程进行计算研究，包括蛋白质动力学、药物设计和酶机制。
              </p>
            </div>
          </div>
        </div>
      </div>
    </V2BaseLayout>
  );
}
