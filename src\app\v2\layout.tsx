import { Metadata } from 'next';
import V2Header from '@/components/v2/Header';
import V2Footer from '@/components/v2/Footer';
import '@/styles/v2.css';

export const metadata: Metadata = {
  title: 'Yangtze Research Group - Modern Interface',
  description: 'Modern interface for Yangtze Research Group - Theoretical and Computational Chemistry',
};

export default function V2Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="v2-theme v2-background min-h-screen flex flex-col">
      <V2Header />
      <main className="relative z-10 flex-1">
        {children}
      </main>
      <V2Footer />
    </div>
  );
}
