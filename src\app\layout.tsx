import { Metadata } from 'next';

import './globals.css';

import { VersionProvider } from '@/lib/version-context';

export const metadata: Metadata = {
  title: `yangtze_web_nextjs`,
  description: `yangtze_web_nextjs`,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <VersionProvider>
          {children}
        </VersionProvider>
      </body>
    </html>
  );
}
