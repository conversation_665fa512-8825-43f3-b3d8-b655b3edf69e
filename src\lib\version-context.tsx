'use client';
import { createContext, useContext, useState, useEffect } from 'react';

type Version = 'v1' | 'v2';

interface VersionContextType {
  version: Version;
  setVersion: (version: Version) => void;
}

const VersionContext = createContext<VersionContextType | undefined>(undefined);

export const useVersion = () => {
  const context = useContext(VersionContext);
  if (!context) {
    throw new Error('useVersion must be used within VersionProvider');
  }
  return context;
};

export const VersionProvider = ({ children }: { children: React.ReactNode }) => {
  const [version, setVersion] = useState<Version>('v1');

  useEffect(() => {
    // 从localStorage获取版本信息
    const savedVersion = localStorage.getItem('preferred-version') as Version;
    if (savedVersion && (savedVersion === 'v1' || savedVersion === 'v2')) {
      setVersion(savedVersion);
    }
  }, []);

  const handleSetVersion = (newVersion: Version) => {
    setVersion(newVersion);
    localStorage.setItem('preferred-version', newVersion);
  };

  return (
    <VersionContext.Provider value={{ version, setVersion: handleSetVersion }}>
      {children}
    </VersionContext.Provider>
  );
};
