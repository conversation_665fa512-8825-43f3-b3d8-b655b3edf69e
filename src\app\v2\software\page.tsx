import V2BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2SoftwarePage() {
  return (
    <V2BaseLayout>
      <div>
        <h1 className="v2-heading text-4xl mb-8 text-white text-center">
          软件与工具
        </h1>

        <div className="v2-card p-8 mb-8">
          <h2 className="v2-heading text-3xl mb-6">LODESTAR</h2>
          <p className="v2-text text-lg mb-6">
            LODESTAR是我们开发的基于局域密度矩阵(LDM)方法的线性标度量子化学程序，
            专门用于大分子系统的高效计算。
          </p>

          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="v2-heading text-xl mb-4">Features</h3>
              <ul className="v2-text space-y-2">
                <li>• Linear-scaling electronic structure calculations</li>
                <li>• Large molecular system support</li>
                <li>• Efficient parallel computing</li>
                <li>• Advanced quantum mechanical methods</li>
              </ul>
            </div>

            <div>
              <h3 className="v2-heading text-xl mb-4">Applications</h3>
              <ul className="v2-text space-y-2">
                <li>• Polymer systems</li>
                <li>• Biological molecules</li>
                <li>• Nanomaterials</li>
                <li>• Surface chemistry</li>
              </ul>
            </div>
          </div>

          <div className="mt-8 text-center">
            <button className="v2-button">
              Download LODESTAR
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Documentation</h3>
            <p className="v2-text mb-4">
              Comprehensive documentation and user guides for all software packages.
            </p>
            <button className="v2-button">
              View Documentation
            </button>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Support</h3>
            <p className="v2-text mb-4">
              Technical support and community forums for software users.
            </p>
            <button className="v2-button">
              Get Support
            </button>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Tutorials</h3>
            <p className="v2-text mb-4">
              Step-by-step tutorials and example calculations.
            </p>
            <button className="v2-button">
              View Tutorials
            </button>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Source Code</h3>
            <p className="v2-text mb-4">
              Access to source code and development resources.
            </p>
            <button className="v2-button">
              GitHub Repository
            </button>
          </div>
        </div>
      </div>
    </V2BaseLayout>
  );
}
