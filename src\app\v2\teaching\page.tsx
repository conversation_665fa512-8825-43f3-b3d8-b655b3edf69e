import V2BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2TeachingPage() {
  return (
    <V2BaseLayout>
      <div>
        <h1 className="v2-heading text-4xl mb-8 text-white text-center">
          教学与教育
        </h1>

        <div className="v2-card p-8 max-w-4xl mx-auto">
          <div className="space-y-6">
            <div>
              <h2 className="v2-heading text-2xl mb-4">本科生课程</h2>
              <p className="v2-text">
                为本科生开设物理化学、计算化学等基础课程，介绍理论化学的基本原理和计算方法。
              </p>
            </div>

            <div>
              <h2 className="v2-heading text-2xl mb-4">研究生课程</h2>
              <p className="v2-text">
                为研究生开设高级量子化学、分子建模等专业课程，深入探讨理论化学的前沿方法。
              </p>
            </div>

            <div>
              <h2 className="v2-heading text-2xl mb-4">教学理念</h2>
              <p className="v2-text">
                我们注重理论与实践相结合，培养学生的科学思维和创新能力，为理论化学领域培养优秀人才。
              </p>
            </div>
          </div>
        </div>

        <div className="v2-card p-8 mb-8">
          <h2 className="v2-heading text-2xl mb-6">Course Materials</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">PDF</span>
              </div>
              <h3 className="v2-heading text-lg mb-2">Lecture Notes</h3>
              <p className="v2-text text-sm">
                Comprehensive lecture materials and notes
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">LAB</span>
              </div>
              <h3 className="v2-heading text-lg mb-2">Lab Exercises</h3>
              <p className="v2-text text-sm">
                Hands-on computational exercises and tutorials
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                <span className="text-white font-bold">VID</span>
              </div>
              <h3 className="v2-heading text-lg mb-2">Video Lectures</h3>
              <p className="v2-text text-sm">
                Recorded lectures and demonstrations
              </p>
            </div>
          </div>
        </div>

        <div className="v2-card p-8">
          <h2 className="v2-heading text-2xl mb-6">Student Opportunities</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="v2-heading text-lg mb-3">Research Projects</h3>
              <p className="v2-text">
                Undergraduate and graduate research opportunities in computational chemistry
              </p>
            </div>
            <div>
              <h3 className="v2-heading text-lg mb-3">Internships</h3>
              <p className="v2-text">
                Summer research internships and exchange programs
              </p>
            </div>
          </div>
        </div>
      </div>
    </V2BaseLayout>
  );
}
