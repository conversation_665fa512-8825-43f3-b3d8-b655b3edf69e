import { Metadata } from 'next';
import V2Header from '@/components/v2/Header';
import V2Footer from '@/components/v2/Footer';
import V2BaseLayout from '@/components/v2/layouts/BaseLayout';
import '@/styles/v2.css';

export const metadata: Metadata = {
  title: 'Yangtze Research Group - Modern Interface',
  description: 'Modern interface for Yangtze Research Group - Theoretical and Computational Chemistry',
};

export default function HomePage() {
  return (
    <div className="v2-theme v2-background min-h-screen flex flex-col">
      <V2Header />
      <main className="relative z-10 flex-1">
        <V2BaseLayout>
          <div className="text-center">
            <h1 className="v2-heading text-5xl mb-6 text-white">欢迎来到长江研究组</h1>
            <p className="v2-text text-xl mb-8 text-white/90 max-w-3xl mx-auto">
              这是现代版本的主页，专注于理论与计算化学的前沿研究
            </p>

            <div className="v2-card p-8 max-w-2xl mx-auto mt-16">
              <h3 className="v2-heading text-2xl mb-4">关于我们</h3>
              <p className="v2-text text-left">
                长江研究组致力于理论与计算化学的研究，开发创新的计算方法来理解分子系统。
                我们的研究涵盖量子化学方法、分子动力学模拟、材料科学和生物系统等多个领域。
              </p>
              <p className="v2-text text-left mt-4">本网站的现代版本正在开发中，将为您提供更好的用户体验和更丰富的功能。</p>
            </div>
          </div>
        </V2BaseLayout>
      </main>
      <V2Footer />
    </div>
  );
}
