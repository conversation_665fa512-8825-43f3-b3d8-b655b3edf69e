import V2BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2LinksPage() {
  return (
    <V2BaseLayout>
      <div>
        <h1 className="v2-heading text-4xl mb-8 text-white text-center">
          有用链接
        </h1>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Academic Resources</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Journal of Chemical Theory and Computation
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Chemical Reviews
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Nature Chemistry
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Physical Chemistry Chemical Physics
              </a>
            </div>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Software Tools</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Gaussian
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • VASP
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • GROMACS
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • AMBER
              </a>
            </div>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Databases</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Protein Data Bank
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Cambridge Structural Database
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • NIST Chemistry WebBook
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Materials Project
              </a>
            </div>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Universities</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • University of Hong Kong
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • MIT
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Stanford University
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Cambridge University
              </a>
            </div>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Research Groups</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Theoretical Chemistry Groups
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Computational Chemistry Networks
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Materials Science Consortiums
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Quantum Chemistry Communities
              </a>
            </div>
          </div>

          <div className="v2-card p-6">
            <h3 className="v2-heading text-xl mb-4">Conferences</h3>
            <div className="space-y-3">
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • ACS National Meetings
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • WATOC Congress
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • ICQC Conference
              </a>
              <a href="#" className="block v2-text hover:text-blue-600 transition-colors">
                • Gordon Research Conferences
              </a>
            </div>
          </div>
        </div>
      </div>
    </V2BaseLayout>
  );
}
