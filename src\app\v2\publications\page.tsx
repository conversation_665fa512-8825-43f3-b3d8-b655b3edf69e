import V2BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2PublicationsPage() {
  return (
    <V2BaseLayout>
      <div>
        <h1 className="v2-heading text-4xl mb-8 text-white text-center">
          学术出版物
        </h1>

        <div className="v2-card p-8 max-w-4xl mx-auto">
          <p className="v2-text text-lg mb-6">
            这里将展示我们研究组的学术出版物，包括期刊论文、会议论文和专著等。
          </p>

          <div className="space-y-4">
            <div>
              <h3 className="v2-heading text-xl mb-2">主要研究成果</h3>
              <p className="v2-text">
                我们在理论与计算化学领域发表了大量高质量的学术论文，涵盖量子化学方法、分子动力学模拟、材料科学等多个方向。
              </p>
            </div>

            <div>
              <h3 className="v2-heading text-xl mb-2">期刊发表</h3>
              <p className="v2-text">
                研究成果发表在 Journal of Chemical Theory and Computation、Chemical Reviews、Nature Chemistry 等顶级期刊上。
              </p>
            </div>

            <div>
              <h3 className="v2-heading text-xl mb-2">学术影响</h3>
              <p className="v2-text">
                我们的研究在计算化学领域产生了重要影响，在材料科学、药物发现和环境化学等方面都有应用。
              </p>
            </div>
          </div>
        </div>
      </div>
    </V2BaseLayout>
  );
}
