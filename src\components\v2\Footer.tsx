const V2Footer = () => {
  return (
    <footer className="v2-nav mt-auto">
      <div className="v2-container">
        <div className="py-8">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Contact</h3>
              <div className="space-y-2 text-white/80">
                <p>Department of Chemistry</p>
                <p>University of Hong Kong</p>
                <p>Pokfulam Road, Hong Kong</p>
                <p>Email: <EMAIL></p>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Quick Links</h3>
              <div className="space-y-2">
                <a href="/v2/research" className="block text-white/80 hover:text-white transition-colors">
                  Research
                </a>
                <a href="/v2/publications" className="block text-white/80 hover:text-white transition-colors">
                  Publications
                </a>
                <a href="/v2/group" className="block text-white/80 hover:text-white transition-colors">
                  Group Members
                </a>
                <a href="/v2/software" className="block text-white/80 hover:text-white transition-colors">
                  Software
                </a>
              </div>
            </div>

            {/* About */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">About</h3>
              <p className="text-white/80 text-sm">
                Yangtze Research Group focuses on theoretical and computational chemistry,
                developing innovative methods for understanding molecular systems.
              </p>
            </div>
          </div>

          <div className="border-t border-white/20 mt-8 pt-8 text-center">
            <p className="text-white/60 text-sm">
              © 2024 Yangtze Research Group. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default V2Footer;
