/* v2版本现代化样式 */
.v2-theme {
  /* 现代化配色方案 */
  --v2-primary: #2563eb;
  --v2-secondary: #64748b;
  --v2-accent: #06b6d4;
  --v2-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --v2-surface: rgba(255, 255, 255, 0.95);
  --v2-text-primary: #1e293b;
  --v2-text-secondary: #64748b;
  
  /* 现代化字体 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.v2-background {
  background: var(--v2-background);
  min-height: 100vh;
  position: relative;
}

.v2-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
  pointer-events: none;
}

.v2-card {
  background: var(--v2-surface);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.v2-nav {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.v2-button {
  background: linear-gradient(135deg, var(--v2-primary), var(--v2-accent));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.v2-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

.v2-nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.v2-nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.v2-nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.v2-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.v2-section {
  padding: 80px 0;
}

.v2-heading {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  color: var(--v2-text-primary);
}

.v2-text {
  color: var(--v2-text-secondary);
  line-height: 1.6;
}
